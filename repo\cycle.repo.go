package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm"
)

type CycleOption func(repository.IRepository[models.Cycle])

var Cycle = func(c core.IContext, options ...CycleOption) repository.IRepository[models.Cycle] {
	r := repository.New[models.Cycle](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func CycleCurrent() CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		c.Where("status = ?", "current")
	}
}

func CycleOrderBy(pageOptions *core.PageOptions) CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("cycle_start_date DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CycleWithProjectUsages() CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		c.Preload("ProjectUsages.Project")
	}
}

func CycleByDateRange(startDate, endDate string) CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		if startDate != "" && endDate != "" {
			c.Where("cycle_start_date >= ? AND cycle_end_date <= ?", startDate, endDate)
		} else if startDate != "" {
			c.Where("cycle_start_date >= ?", startDate)
		} else if endDate != "" {
			c.Where("cycle_end_date <= ?", endDate)
		}
	}
}

func CycleBySearch(search string) CycleOption {
	if search == "" {
		return func(c repository.IRepository[models.Cycle]) {}
	}
	return func(c repository.IRepository[models.Cycle]) {
		// Search by date range or ID
		c.Where("id::text ILIKE ? OR cycle_start_date::text ILIKE ? OR cycle_end_date::text ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
}

func CycleWithProjectUsagesByProject(projectID string) CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		// Use JOIN with raw SQL subquery to get only cycles that have the latest ProjectUsage for the project
		c.Joins(`JOIN (
			SELECT DISTINCT ON (cycle_id) cycle_id, id, project_id, amount, official_amount, hour_count, timestamp, created_at, updated_at
			FROM project_usages
			WHERE project_id = ?
			ORDER BY cycle_id, timestamp DESC, created_at DESC
		) AS latest_usage ON cycles.id = latest_usage.cycle_id`, projectID).
			Preload("ProjectUsage", func(db *gorm.DB) *gorm.DB {
				return db.Where("project_id = ?", projectID).
					Where("id IN (SELECT DISTINCT ON (cycle_id) id FROM project_usages WHERE project_id = ? ORDER BY cycle_id, timestamp DESC, created_at DESC)", projectID)
			}).
			Order("cycle_start_date DESC")
	}
}
